import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Upload, FileText, User, Phone, Mail, Eye, Car, X } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { api } from '@/lib/api';

interface DrivingLicenseFormProps {
  onClose: () => void;
  onSuccess: (requestId: string) => void;
}

const DrivingLicenseForm: React.FC<DrivingLicenseFormProps> = ({ onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    licenseType: '',
    email: '',
    phone: '',
    eyeTestStatus: ''
  });
  const [files, setFiles] = useState<{
    emirates_id?: File;
    visa?: File;
    photo?: File;
  }>({});
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileChange = (field: string, file: File | null) => {
    if (file) {
      setFiles(prev => ({ ...prev, [field]: file }));
    } else {
      setFiles(prev => {
        const newFiles = { ...prev };
        delete newFiles[field as keyof typeof newFiles];
        return newFiles;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.licenseType || !formData.email || !formData.phone || !formData.eyeTestStatus) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    if (!files.emirates_id || !files.visa || !files.photo) {
      toast({
        title: "Missing Documents",
        description: "Please upload all required documents.",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    
    try {
      const submitData = new FormData();
      
      // Add form data
      Object.entries(formData).forEach(([key, value]) => {
        submitData.append(key, value);
      });
      
      // Add files
      Object.entries(files).forEach(([key, file]) => {
        if (file) {
          submitData.append(key, file);
        }
      });

      const response = await api.submitDrivingLicenseRequest(submitData);
      
      toast({
        title: "Application Submitted!",
        description: "Your driving license application has been submitted successfully. You'll receive updates on your progress.",
      });
      
      onSuccess(response.requestId);
      
    } catch (error) {
      console.error('Submission error:', error);
      toast({
        title: "Submission Failed",
        description: error instanceof Error ? error.message : "Failed to submit application. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const FileUploadField = ({ 
    field, 
    label, 
    description, 
    accept = ".jpg,.jpeg,.png,.pdf" 
  }: { 
    field: string; 
    label: string; 
    description: string; 
    accept?: string; 
  }) => (
    <div className="space-y-2">
      <Label htmlFor={field}>{label} *</Label>
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors">
        {files[field as keyof typeof files] ? (
          <div className="flex items-center justify-between bg-blue-50 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <FileText className="w-4 h-4 text-blue-600" />
              <span className="text-sm text-blue-800">
                {files[field as keyof typeof files]?.name}
              </span>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => handleFileChange(field, null)}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        ) : (
          <div>
            <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-600 mb-2">{description}</p>
            <Input
              id={field}
              type="file"
              accept={accept}
              onChange={(e) => handleFileChange(field, e.target.files?.[0] || null)}
              className="hidden"
            />
            <Label htmlFor={field} className="cursor-pointer">
              <Button type="button" variant="outline" size="sm">
                Choose File
              </Button>
            </Label>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Car className="w-5 h-5 text-blue-600" />
                Driving License Application
              </CardTitle>
              <CardDescription>
                Complete the form below to start your driving license process
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Personal Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <User className="w-5 h-5" />
                Personal Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      className="pl-10"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="phone">Phone Number *</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="phone"
                      placeholder="+971 XX XXX XXXX"
                      className="pl-10"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      required
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* License Details */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Car className="w-5 h-5" />
                License Details
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="licenseType">License Type *</Label>
                  <Select value={formData.licenseType} onValueChange={(value) => handleInputChange('licenseType', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select license type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="automatic">Automatic</SelectItem>
                      <SelectItem value="manual">Manual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="eyeTestStatus">Eye Test Status *</Label>
                  <Select value={formData.eyeTestStatus} onValueChange={(value) => handleInputChange('eyeTestStatus', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select eye test status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="need_assistance">Need Assistance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Document Upload */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Required Documents
              </h3>
              
              <div className="space-y-4">
                <FileUploadField
                  field="emirates_id"
                  label="Emirates ID"
                  description="Upload clear photo of both sides of your Emirates ID"
                />
                
                <FileUploadField
                  field="visa"
                  label="Visa Copy"
                  description="Upload your current UAE visa page"
                />
                
                <FileUploadField
                  field="photo"
                  label="Passport Photo"
                  description="Upload a recent passport-size photo"
                  accept=".jpg,.jpeg,.png"
                />
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex gap-3 pt-4">
              <Button type="button" variant="outline" onClick={onClose} className="flex-1">
                Cancel
              </Button>
              <Button type="submit" disabled={loading} className="flex-1">
                {loading ? "Submitting..." : "Submit Application"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default DrivingLicenseForm;
