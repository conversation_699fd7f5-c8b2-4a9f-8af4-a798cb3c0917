
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  User,
  FileText,
  Clock,
  Shield,
  AlertTriangle,
  CheckCircle,
  Phone,
  Mail,
  LogOut,
  Upload,
  Download,
  Calendar,
  Car,
  Eye,
  RefreshCw
} from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/components/auth/AuthContext';
import { api } from '@/lib/api';

interface ServiceRequest {
  _id: string;
  serviceType: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
  assignedTo?: {
    name: string;
    email: string;
  };
  steps: Array<{
    name: string;
    status: 'pending' | 'in_progress' | 'completed';
    comment?: string;
    date?: string;
    updatedAt: string;
  }>;
  progress?: number;
}

interface DashboardStats {
  totalRequests: number;
  pendingRequests: number;
  inProgressRequests: number;
  completedRequests: number;
}

const CustomerDashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, logout, isAuthenticated } = useAuth();
  const [serviceRequests, setServiceRequests] = useState<ServiceRequest[]>([]);
  const [stats, setStats] = useState<DashboardStats>({
    totalRequests: 0,
    pendingRequests: 0,
    inProgressRequests: 0,
    completedRequests: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('CustomerDashboard: Auth state changed', { isAuthenticated, user: user?.name, role: user?.role });
    if (isAuthenticated && user) {
      fetchDashboardData();
    }
  }, [isAuthenticated, user]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const [requestsResponse, statsResponse] = await Promise.all([
        api.getCustomerRequests(),
        api.getCustomerStats()
      ]);

      if (requestsResponse.success) {
        setServiceRequests(requestsResponse.requests || []);
      }

      if (statsResponse.success) {
        setStats(statsResponse.stats || stats);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      toast({
        title: "Error",
        description: "Failed to load dashboard data. Please refresh the page.",
        variant: "destructive"
      });

      // Set default empty state if API fails
      setServiceRequests([]);
      setStats({
        totalRequests: 0,
        pendingRequests: 0,
        inProgressRequests: 0,
        completedRequests: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Logged out successfully",
        description: "You have been logged out of your account.",
      });
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
      navigate('/');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'expired': return 'bg-red-100 text-red-800';
      case 'expiring_soon': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'in_progress': return <Clock className="w-4 h-4" />;
      case 'expired': case 'expiring_soon': return <AlertTriangle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  // Show loading state if not authenticated or user data is not available
  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-12 h-12 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-600 text-white p-2 rounded-lg">
                <User className="w-6 h-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Welcome, {user?.name || 'Customer'}</h1>
                <p className="text-sm text-gray-600">Customer Dashboard</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm" onClick={() => navigate('/')}>
                Back to Home
              </Button>
              <Button variant="outline" size="sm" onClick={handleLogout}>
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Message for New Users */}
        {stats.totalRequests === 0 && (
          <div className="mb-8">
            <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="bg-blue-100 p-3 rounded-full">
                    <User className="w-8 h-8 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                      Welcome to UAE Digital Solutions, {user?.name}! 🎉
                    </h2>
                    <p className="text-gray-700 mb-4">
                      Your account is ready! Start by applying for our most popular service - driving license processing.
                      We'll handle everything from document translation to certificate issuance.
                    </p>
                    <div className="flex gap-3">
                      <Button onClick={() => navigate('/services')} className="bg-blue-600 hover:bg-blue-700">
                        <Car className="w-4 h-4 mr-2" />
                        Get Started
                      </Button>
                      <Button variant="outline" onClick={() => navigate('/services')}>
                        Browse All Services
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Dashboard Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-blue-600">{stats.totalRequests}</p>
                  <p className="text-sm text-gray-600">Total Requests</p>
                </div>
                <FileText className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-yellow-600">{stats.pendingRequests}</p>
                  <p className="text-sm text-gray-600">Pending</p>
                </div>
                <Clock className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-orange-600">{stats.inProgressRequests}</p>
                  <p className="text-sm text-gray-600">In Progress</p>
                </div>
                <RefreshCw className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-green-600">{stats.completedRequests}</p>
                  <p className="text-sm text-gray-600">Completed</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="services" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="services">My Service Requests</TabsTrigger>
            <TabsTrigger value="new-service">New Service</TabsTrigger>
            <TabsTrigger value="profile">Profile</TabsTrigger>
          </TabsList>

          <TabsContent value="services">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>My Service Requests</CardTitle>
                    <CardDescription>Track the progress of your service applications</CardDescription>
                  </div>
                  <Button onClick={fetchDashboardData} variant="outline">
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Refresh
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">
                    <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
                    <p className="text-gray-600">Loading your service requests...</p>
                  </div>
                ) : serviceRequests.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="bg-blue-50 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
                      <Car className="w-12 h-12 text-blue-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Welcome to Your Dashboard!</h3>
                    <p className="text-gray-600 mb-6 max-w-md mx-auto">
                      You haven't submitted any service requests yet. Get started by applying for our most popular service - driving license processing!
                    </p>
                    <div className="space-y-3">
                      <Button onClick={() => navigate('/services')} className="bg-blue-600 hover:bg-blue-700">
                        <Car className="w-4 h-4 mr-2" />
                        Apply for Driving License
                      </Button>
                      <div className="text-sm text-gray-500">
                        <p>✓ Complete document processing</p>
                        <p>✓ Theory classes & training</p>
                        <p>✓ Certificate issuance</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {serviceRequests.map((request) => (
                      <Card key={request._id} className="border-l-4 border-l-blue-500">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-3">
                              <Car className="w-6 h-6 text-blue-600" />
                              <div>
                                <h3 className="font-semibold text-lg">
                                  {request.serviceType === 'driving_license' ? 'Driving License Application' : request.serviceType}
                                </h3>
                                <p className="text-sm text-gray-600">
                                  Request ID: {request._id.slice(-8)}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-3">
                              <Badge className={getStatusColor(request.status)}>
                                {request.status.replace('_', ' ').toUpperCase()}
                              </Badge>
                              {request.assignedTo && (
                                <div className="text-sm text-gray-600">
                                  <p>Assigned to: {request.assignedTo.name}</p>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Progress Bar */}
                          <div className="mb-4">
                            <div className="flex justify-between text-sm mb-2">
                              <span>Progress</span>
                              <span>{request.progress || 0}%</span>
                            </div>
                            <Progress value={request.progress || 0} className="h-2" />
                          </div>

                          {/* Steps */}
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mb-4">
                            {request.steps.slice(0, 6).map((step, index) => (
                              <div key={step.name} className="flex items-center gap-2 text-sm">
                                {step.status === 'completed' ? (
                                  <CheckCircle className="w-4 h-4 text-green-600" />
                                ) : step.status === 'in_progress' ? (
                                  <RefreshCw className="w-4 h-4 text-blue-600" />
                                ) : (
                                  <Clock className="w-4 h-4 text-gray-400" />
                                )}
                                <span className={step.status === 'completed' ? 'text-green-700' : step.status === 'in_progress' ? 'text-blue-700' : 'text-gray-600'}>
                                  {step.name.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                </span>
                              </div>
                            ))}
                          </div>

                          <div className="flex justify-between items-center text-sm text-gray-600">
                            <span>Submitted: {new Date(request.createdAt).toLocaleDateString()}</span>
                            <span>Last Updated: {new Date(request.updatedAt).toLocaleDateString()}</span>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="new-service">
            <Card>
              <CardHeader>
                <CardTitle>Apply for New Service</CardTitle>
                <CardDescription>Start a new service application</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Driving License Service */}
                  <Card className="border-2 border-blue-200 bg-blue-50">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <Car className="w-8 h-8 text-blue-600" />
                        <div>
                          <h3 className="font-semibold text-lg">Driving License</h3>
                          <p className="text-sm text-gray-600">Complete driving license processing</p>
                        </div>
                        <Badge className="bg-green-100 text-green-800">Available</Badge>
                      </div>
                      <div className="space-y-2 mb-4">
                        <div className="flex items-center gap-2 text-sm text-gray-700">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span>Document Translation</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-700">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span>Theory Classes & Training</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-700">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span>Certificate Issuance</span>
                        </div>
                      </div>
                      <Button
                        className="w-full"
                        onClick={() => navigate('/services')}
                      >
                        Apply Now
                      </Button>
                    </CardContent>
                  </Card>

                  {/* Coming Soon Services */}
                  <Card className="border-2 border-gray-200 bg-gray-50 opacity-75">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <FileText className="w-8 h-8 text-gray-400" />
                        <div>
                          <h3 className="font-semibold text-lg text-gray-600">Other Services</h3>
                          <p className="text-sm text-gray-500">Visa, Typing, Business Setup</p>
                        </div>
                        <Badge variant="secondary">Coming Soon</Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">
                        We're expanding our services to serve you better. These services will be available soon!
                      </p>
                      <Button disabled className="w-full">
                        <Clock className="w-4 h-4 mr-2" />
                        Notify Me
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="profile">
            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>Manage your account details and preferences</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center gap-4 mb-6">
                  {user?.avatar ? (
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-16 h-16 rounded-full"
                    />
                  ) : (
                    <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center">
                      <User className="w-8 h-8 text-blue-600" />
                    </div>
                  )}
                  <div>
                    <h3 className="text-xl font-semibold">{user?.name}</h3>
                    <p className="text-gray-600">{user?.role?.toUpperCase()} Account</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Full Name</label>
                    <p className="mt-1 text-sm text-gray-900">{user?.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Email</label>
                    <p className="mt-1 text-sm text-gray-900">{user?.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Account Type</label>
                    <p className="mt-1 text-sm text-gray-900">
                      <Badge className="bg-blue-100 text-blue-800">
                        {user?.role?.toUpperCase()}
                      </Badge>
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">User ID</label>
                    <p className="mt-1 text-sm text-gray-900 font-mono">{user?.id}</p>
                  </div>
                </div>

                <div className="pt-6 border-t">
                  <div className="flex gap-3">
                    <Button onClick={() => navigate('/services')}>
                      Browse Services
                    </Button>
                    <Button variant="outline" onClick={handleLogout}>
                      <LogOut className="w-4 h-4 mr-2" />
                      Logout
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default CustomerDashboard;
