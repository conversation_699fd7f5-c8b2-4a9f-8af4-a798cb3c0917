const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: function() {
      // Password is required only if googleId is not present
      return !this.googleId;
    },
    minlength: 6
  },
  googleId: {
    type: String,
    sparse: true // Allows multiple null values
  },
  role: {
    type: String,
    enum: ['customer', 'staff', 'admin'],
    default: 'customer'
  },
  avatar: {
    type: String // Google profile picture URL
  },
  isActive: {
    type: Boolean,
    default: true
  },
  emailVerified: {
    type: Boolean,
    default: false
  },
  verificationToken: {
    type: String
  },
  resetPasswordToken: {
    type: String
  },
  resetPasswordExpires: {
    type: Date
  }
}, {
  timestamps: true
});

// Pre-save middleware to hash password and assign roles
userSchema.pre('save', async function(next) {
  // Hash password if it's modified and not a Google user
  if (this.isModified('password') && this.password) {
    try {
      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.password, salt);
    } catch (error) {
      return next(error);
    }
  }

  // Define admin emails (highest priority)
  const adminEmails = [
    '<EMAIL>',
    '<EMAIL>',
    process.env.ADMIN_EMAIL
  ].filter(Boolean); // Remove any undefined values

  // Define staff emails (medium priority)
  const staffEmails = [
    process.env.STAFF_EMAIL
  ].filter(Boolean);

  // Assign roles based on priority: admin > staff > customer
  if (adminEmails.includes(this.email)) {
    this.role = 'admin';
  } else if (staffEmails.includes(this.email)) {
    this.role = 'staff';
  }
  // If no special email match, keep default 'customer' role

  next();
});

// Instance method to check if user has specific role
userSchema.methods.hasRole = function(role) {
  return this.role === role;
};

// Instance method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  if (!this.password) {
    return false; // Google users don't have passwords
  }
  return bcrypt.compare(candidatePassword, this.password);
};

// Static method to find users by role
userSchema.statics.findByRole = function(role) {
  return this.find({ role: role, isActive: true });
};

module.exports = mongoose.model('User', userSchema);
