import React, { useState, useEffect } from 'react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Users, 
  FileText, 
  Clock,
  CheckCircle,
  AlertTriangle,
  Calendar,
  Car,
  RefreshCw,
  UserPlus,
  Eye
} from 'lucide-react';
import { api } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

interface ServiceRequest {
  _id: string;
  serviceType: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
  customerId: {
    _id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  assignedTo?: {
    _id: string;
    name: string;
    email: string;
  };
  customerData: {
    licenseType: string;
    email: string;
    phone: string;
    eyeTestStatus: string;
    documents: Array<{
      type: string;
      filename: string;
      originalName: string;
    }>;
  };
  steps: Array<{
    name: string;
    status: 'pending' | 'in_progress' | 'completed';
  }>;
}

interface StaffMember {
  _id: string;
  name: string;
  email: string;
  role: string;
}

interface DashboardStats {
  totalRequests: number;
  pendingRequests: number;
  inProgressRequests: number;
  completedRequests: number;
  totalStaff: number;
  totalCustomers: number;
}

const AdminDashboard = () => {
  const [serviceRequests, setServiceRequests] = useState<ServiceRequest[]>([]);
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);
  const [stats, setStats] = useState<DashboardStats>({
    totalRequests: 0,
    pendingRequests: 0,
    inProgressRequests: 0,
    completedRequests: 0,
    totalStaff: 0,
    totalCustomers: 0
  });
  const [loading, setLoading] = useState(true);
  const [assigningRequest, setAssigningRequest] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const [requestsResponse, staffResponse, statsResponse] = await Promise.all([
        api.getAdminRequests(),
        api.getStaffMembers(),
        api.getAdminStats()
      ]);
      
      setServiceRequests(requestsResponse.requests || []);
      setStaffMembers(staffResponse.staff || []);
      setStats(statsResponse.stats || stats);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      toast({
        title: "Error",
        description: "Failed to load dashboard data. Please refresh the page.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAssignRequest = async (requestId: string, staffEmail: string) => {
    try {
      setAssigningRequest(requestId);
      await api.assignRequest(requestId, staffEmail);
      
      toast({
        title: "Request Assigned",
        description: "Service request has been successfully assigned to staff member.",
      });
      
      // Refresh data
      await fetchDashboardData();
    } catch (error) {
      console.error('Failed to assign request:', error);
      toast({
        title: "Assignment Failed",
        description: error instanceof Error ? error.message : "Failed to assign request. Please try again.",
        variant: "destructive"
      });
    } finally {
      setAssigningRequest(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getProgress = (steps: ServiceRequest['steps']) => {
    const completedSteps = steps.filter(step => step.status === 'completed').length;
    return Math.round((completedSteps / steps.length) * 100);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in_progress':
        return <RefreshCw className="h-4 w-4 text-blue-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <AdminSidebar />
      
      <main className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
                <p className="text-sm text-gray-600">Welcome back! Here's what's happening today.</p>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Calendar className="h-4 w-4" />
                {new Date().toLocaleDateString('en-GB', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </div>
            </div>
          </div>

          <div className="p-6 space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Requests</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.totalRequests}</p>
                      <p className="text-xs text-gray-500 mt-1">All service requests</p>
                    </div>
                    <FileText className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Pending</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.pendingRequests}</p>
                      <p className="text-xs text-gray-500 mt-1">Awaiting assignment</p>
                    </div>
                    <Clock className="h-8 w-8 text-yellow-600" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">In Progress</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.inProgressRequests}</p>
                      <p className="text-xs text-gray-500 mt-1">Being processed</p>
                    </div>
                    <RefreshCw className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Staff Members</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.totalStaff}</p>
                      <p className="text-xs text-gray-500 mt-1">Active staff</p>
                    </div>
                    <Users className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Service Requests */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Car className="h-5 w-5 text-blue-600" />
                    Service Requests
                  </CardTitle>
                  <Button onClick={fetchDashboardData} variant="outline" size="sm">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">
                    <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
                    <p className="text-gray-600">Loading service requests...</p>
                  </div>
                ) : serviceRequests.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No Service Requests</h3>
                    <p className="text-gray-600">No service requests have been submitted yet.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {serviceRequests.slice(0, 5).map((request) => (
                      <Card key={request._id} className="border-l-4 border-l-blue-500">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center gap-2">
                                {request.customerId.avatar ? (
                                  <img 
                                    src={request.customerId.avatar} 
                                    alt={request.customerId.name}
                                    className="w-8 h-8 rounded-full"
                                  />
                                ) : (
                                  <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                                    <Users className="w-4 h-4 text-blue-600" />
                                  </div>
                                )}
                                <div>
                                  <p className="font-semibold text-gray-900">{request.customerId.name}</p>
                                  <p className="text-sm text-gray-600">{request.customerId.email}</p>
                                </div>
                              </div>
                              <Badge className={getStatusColor(request.status)}>
                                {request.status.replace('_', ' ').toUpperCase()}
                              </Badge>
                            </div>
                            <div className="text-right">
                              <p className="text-sm text-gray-600">
                                {new Date(request.createdAt).toLocaleDateString()}
                              </p>
                              <p className="text-xs text-gray-500">
                                Progress: {getProgress(request.steps)}%
                              </p>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                            <div>
                              <p className="text-sm font-medium text-gray-700">Service Type</p>
                              <p className="text-sm text-gray-900">
                                {request.serviceType === 'driving_license' ? 'Driving License' : request.serviceType}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-700">License Type</p>
                              <p className="text-sm text-gray-900">{request.customerData.licenseType}</p>
                            </div>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              {request.assignedTo ? (
                                <div className="flex items-center gap-2">
                                  <UserPlus className="w-4 h-4 text-green-600" />
                                  <span className="text-sm text-green-700">
                                    Assigned to: {request.assignedTo.name}
                                  </span>
                                </div>
                              ) : (
                                <span className="text-sm text-yellow-600">Unassigned</span>
                              )}
                            </div>
                            
                            <div className="flex gap-2">
                              {!request.assignedTo && (
                                <Select
                                  onValueChange={(staffEmail) => handleAssignRequest(request._id, staffEmail)}
                                  disabled={assigningRequest === request._id}
                                >
                                  <SelectTrigger className="w-40">
                                    <SelectValue placeholder="Assign to..." />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {staffMembers.map((staff) => (
                                      <SelectItem key={staff._id} value={staff.email}>
                                        {staff.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              )}
                              <Button variant="outline" size="sm">
                                <Eye className="w-4 h-4 mr-1" />
                                View Details
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default AdminDashboard;
