const express = require('express');
const ServiceRequest = require('../models/ServiceRequest.cjs');
const { requireAuth } = require('../middleware/auth.cjs');

const router = express.Router();

// All customer routes require authentication
router.use(requireAuth);

// Get customer's service requests
router.get('/requests', async (req, res) => {
  try {
    const requests = await ServiceRequest.findByCustomer(req.user._id)
      .populate('assignedTo', 'name email')
      .sort({ createdAt: -1 });

    // Add progress calculation to each request
    const requestsWithProgress = requests.map(request => {
      const progress = request.getProgress();
      return {
        ...request.toObject(),
        progress
      };
    });

    res.json({
      success: true,
      requests: requestsWithProgress
    });
  } catch (error) {
    console.error('Get customer requests error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get service requests',
      error: error.message
    });
  }
});

// Get specific service request with detailed progress
router.get('/requests/:id', async (req, res) => {
  try {
    const serviceRequest = await ServiceRequest.findById(req.params.id)
      .populate('customerId', 'name email')
      .populate('assignedTo', 'name email')
      .populate('steps.updatedBy', 'name')
      .populate('notes.addedBy', 'name');
    
    if (!serviceRequest) {
      return res.status(404).json({
        success: false,
        message: 'Service request not found'
      });
    }
    
    // Check if user owns this request
    if (serviceRequest.customerId._id.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    // Add progress percentage
    const progress = serviceRequest.getProgress();
    
    res.json({
      success: true,
      request: {
        ...serviceRequest.toObject(),
        progress
      }
    });
    
  } catch (error) {
    console.error('Get service request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get service request',
      error: error.message
    });
  }
});

// Get customer dashboard statistics
router.get('/dashboard/stats', async (req, res) => {
  try {
    const totalRequests = await ServiceRequest.countDocuments({ customerId: req.user._id });
    const pendingRequests = await ServiceRequest.countDocuments({ 
      customerId: req.user._id, 
      status: 'pending' 
    });
    const inProgressRequests = await ServiceRequest.countDocuments({ 
      customerId: req.user._id, 
      status: 'in_progress' 
    });
    const completedRequests = await ServiceRequest.countDocuments({ 
      customerId: req.user._id, 
      status: 'completed' 
    });
    
    res.json({
      success: true,
      stats: {
        totalRequests,
        pendingRequests,
        inProgressRequests,
        completedRequests
      }
    });
  } catch (error) {
    console.error('Get customer dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get dashboard statistics',
      error: error.message
    });
  }
});

module.exports = router;
