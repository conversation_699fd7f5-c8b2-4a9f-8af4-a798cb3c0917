const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

// API utility functions
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config: RequestInit = {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Auth endpoints
  async getAuthStatus() {
    return this.request('/api/auth/status');
  }

  async logout() {
    return this.request('/api/auth/logout', { method: 'POST' });
  }

  // Services endpoints
  async getServices() {
    return this.request('/api/services');
  }

  async submitDrivingLicenseRequest(formData: FormData) {
    return fetch(`${this.baseURL}/api/services/driving-license/submit`, {
      method: 'POST',
      credentials: 'include',
      body: formData, // Don't set Content-Type for FormData
    }).then(async (response) => {
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }
      return response.json();
    });
  }

  // Customer dashboard endpoints
  async getCustomerRequests() {
    return this.request('/api/customer/requests');
  }

  async getCustomerStats() {
    return this.request('/api/customer/dashboard/stats');
  }

  async getServiceRequest(id: string) {
    return this.request(`/api/services/request/${id}`);
  }

  // Customer endpoints
  async getCustomerRequests() {
    return this.request('/api/customer/requests');
  }

  async getCustomerRequest(id: string) {
    return this.request(`/api/customer/requests/${id}`);
  }

  async getCustomerStats() {
    return this.request('/api/customer/dashboard/stats');
  }

  async trackServiceRequest(id: string) {
    return this.request(`/api/customer/track/${id}`);
  }

  // Staff endpoints
  async getStaffRequests() {
    return this.request('/api/staff/requests');
  }

  async getStaffRequest(id: string) {
    return this.request(`/api/staff/requests/${id}`);
  }

  async updateWorkflowStep(requestId: string, stepName: string, data: any, files?: FileList) {
    const formData = new FormData();
    
    // Add form data
    Object.keys(data).forEach(key => {
      if (data[key] !== undefined && data[key] !== null) {
        formData.append(key, data[key]);
      }
    });

    // Add files if provided
    if (files) {
      Array.from(files).forEach(file => {
        formData.append('documents', file);
      });
    }

    return fetch(`${this.baseURL}/api/staff/requests/${requestId}/steps/${stepName}`, {
      method: 'PATCH',
      credentials: 'include',
      body: formData,
    }).then(async (response) => {
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }
      return response.json();
    });
  }

  async addNoteToRequest(requestId: string, content: string) {
    return this.request(`/api/staff/requests/${requestId}/notes`, {
      method: 'POST',
      body: JSON.stringify({ content }),
    });
  }

  async getStaffStats() {
    return this.request('/api/staff/dashboard/stats');
  }

  // Admin endpoints
  async getAdminRequests() {
    return this.request('/api/admin/requests');
  }

  async getStaffMembers() {
    return this.request('/api/admin/staff');
  }

  async assignRequest(requestId: string, staffEmail: string) {
    return this.request('/api/admin/assign', {
      method: 'POST',
      body: JSON.stringify({ requestId, staffEmail }),
    });
  }

  async updateRequestStatus(requestId: string, status: string) {
    return this.request(`/api/admin/requests/${requestId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status }),
    });
  }

  async reassignRequest(requestId: string, staffEmail: string) {
    return this.request(`/api/admin/requests/${requestId}/reassign`, {
      method: 'PATCH',
      body: JSON.stringify({ staffEmail }),
    });
  }

  async getAdminStats() {
    return this.request('/api/admin/dashboard/stats');
  }
}

// Create and export API client instance
export const api = new ApiClient(API_URL);

// Export API URL for direct use
export { API_URL };
