import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/components/auth/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Shield, Users, User, Building2 } from 'lucide-react';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

const RoleSelection = () => {
  const navigate = useNavigate();
  const { user, refreshUser } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const handleRoleSelect = async (role: 'customer' | 'staff' | 'admin') => {
    setIsLoading(true);
    
    try {
      const response = await fetch(`${API_URL}/api/auth/change-role`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ role }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`Role changed to ${role}`);

        // Refresh user data
        await refreshUser();

        // Redirect to appropriate dashboard
        const redirectPath = role === 'admin'
          ? '/admin/dashboard'
          : role === 'staff'
          ? '/staff/dashboard'
          : '/customer/dashboard';

        navigate(redirectPath);
      } else {
        toast.error(data.message || 'Failed to change role');
      }
    } catch (error) {
      toast.error('Failed to change role');
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    navigate('/');
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <Building2 className="mx-auto h-12 w-12 text-blue-600 mb-4" />
          <CardTitle>Select Your Role</CardTitle>
          <CardDescription>
            Choose how you want to access the system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            onClick={() => handleRoleSelect('customer')}
            disabled={isLoading}
            className="w-full h-16 flex items-center justify-start space-x-4 bg-green-600 hover:bg-green-700"
          >
            <User className="h-6 w-6" />
            <div className="text-left">
              <div className="font-semibold">Customer</div>
              <div className="text-sm opacity-90">Submit and track service requests</div>
            </div>
          </Button>

          <Button
            onClick={() => handleRoleSelect('staff')}
            disabled={isLoading}
            className="w-full h-16 flex items-center justify-start space-x-4 bg-blue-600 hover:bg-blue-700"
          >
            <Users className="h-6 w-6" />
            <div className="text-left">
              <div className="font-semibold">Staff</div>
              <div className="text-sm opacity-90">Process customer requests</div>
            </div>
          </Button>

          <Button
            onClick={() => handleRoleSelect('admin')}
            disabled={isLoading}
            className="w-full h-16 flex items-center justify-start space-x-4 bg-purple-600 hover:bg-purple-700"
          >
            <Shield className="h-6 w-6" />
            <div className="text-left">
              <div className="font-semibold">Admin</div>
              <div className="text-sm opacity-90">Manage system and users</div>
            </div>
          </Button>

          <div className="text-center pt-4 border-t">
            <p className="text-sm text-gray-600 mb-2">
              Logged in as: <strong>{user.email}</strong>
            </p>
            <p className="text-xs text-gray-500 mb-3">
              Current role: <strong>{user.role}</strong>
            </p>

            <Button
              onClick={() => handleRoleSelect('customer')}
              disabled={isLoading}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              Reset to Customer
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RoleSelection;
