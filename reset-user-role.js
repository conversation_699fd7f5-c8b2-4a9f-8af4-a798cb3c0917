require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User.cjs');

async function resetUserRole() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Find the user with the specific email
    const email = process.env.STAFF_EMAIL || '<EMAIL>';
    const user = await User.findOne({ email: email });

    if (user) {
      console.log(`Found user: ${user.name} (${user.email})`);
      console.log(`Current role: ${user.role}`);
      
      // Reset role to customer
      user.role = 'customer';
      await user.save();
      
      console.log(`Role updated to: ${user.role}`);
    } else {
      console.log(`No user found with email: ${email}`);
    }

    // Close connection
    await mongoose.connection.close();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

resetUserRole();
